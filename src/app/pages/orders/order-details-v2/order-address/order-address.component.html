<app-card
  [labelKey]="'orderDetails.jobAddress.title'"
  [buttonHeader]="!viewSettings?.contractorNotAcceptedView"
  [padding]="'0'"
  [buttonText]="'common.edit'"
  [buttonType]="'link'"
  [xsmall]="true">

  <div buttonHeader>
    <app-button [customClass]="'me-2 p-0'" [translationKey]="'common.add'" [buttonType]="'link'" (buttonClick)="addEmptyAddress()" [small]="true"></app-button>
  </div>
  <ng-container cardcontent>
    <div class="accordion accordion-flush" id="accordionFlushNoAddresses" *ngIf="addresses.length === 0">
      <div class="my-2 ms-3">
        {{ "orders.newOrder.address.noAddresses" | translate}}
      </div>
    </div>
    <div class="accordion accordion-flush" id="accordionFlushExample" *ngIf="addresses" cdkDropList cdkDropListOrientation="vertical" cdkDropListLockAxis="y" (cdkDropListDropped)="drop($event)" [cdkDropListDisabled]="addresses.length < 2">
      <div *ngFor="let addrObj of addresses; let j = index" cdkDrag class="bg-white address-object-container">
        <div class="accordion-item" [ngClass]="{'border-top': j > 0, 'border-bottom': j === addresses.length - 1}">
          <div class="accordion-header d-flex" [id]="'flush-heading-' + j">
            <div cdkDragHandle class="d-flex align-items-center cursor-pointer drag-handle-div px-1">
              <i *ngIf="addresses.length > 1" class="fa-regular fa-grip-dots-vertical fa-xl"></i>
            </div>
            <div *ngIf="!addrObj.empty" class="accordion-button ps-1" [ngClass]="addrObj.collapsed ? 'collapsed' : ''" [id]="'#flush-collapse-' + j" type="button" (click)="toggleCollapse(addrObj)">
              <div class="col-9">
                <div class="d-flex" (click)="$event.stopPropagation(); addrObj.editMode = true;" [ngClass]="{'mb-1': !order || order.work_orders.length < 2}">
                  <app-input
                    [control]="addrObj.nameControl"
                    [type]="'text'"
                    [editMode]="addrObj.editMode"
                    [placeholderKey]="'orders.newOrder.address.addressName'"
                    [emitChangeOnBlurOnly]="true"
                    (valueChange)="updateAddressName(addrObj)"
                    (onEnterPressed)="updateAddressName(addrObj)"
                    (onEscapePressed)="resetAddressName(addrObj)"
                  ></app-input>
                </div>
                <div *ngIf="order && order.work_orders.length > 1 && !workOrderView" class="text-muted fw-light">{{addrObj.workOrderIds.length < 2 ? addrObj.workOrder.work_order_title : ("orderDetails.addresses.multipleWorkOrders" | translate)}}</div>
                <div class="fw-light">{{ addrObj.address.display }}</div>
              </div>
              <div class="col-3 d-flex justify-content-end pe-2" (mouseenter)="nonToggleHover = true" (mouseleave)="nonToggleHover = false">
                <i *ngIf="!addrObj.empty && (!addrObj.address.lat || !addrObj.address.lng)" class="fa-solid fa-location-dot-slash cursor-pointer" [ngbTooltip]="'orders.newOrder.address.noLatLng' | translate"></i>
                <i *ngIf="!addrObj.messageVisible && !addrObj.empty" class="fa fa-eye icon-button font-16 cursor-pointer" style="color: #6C757D;" (click)="openStreetViewModal(addrObj)" onmouseover="this.style.color='black'" onmouseout="this.style.color='#6C757D'"></i>
                <i *ngIf="addrObj.messageVisible && !addrObj.empty" class="fa fa-eye-slash font-16 cursor-pointer" [ngbTooltip]="'orders.newOrder.address.noStreetView' | translate"></i>
                <i *ngIf="!addrObj.empty" class="fa fa-pen-to-square font-16 icon-button" style="color: #6C757D" [ngClass]="addrObj.deletable ? 'ms-1' : 'ms-3'" (click)="openEditAddressModal(addrObj)"  onmouseover="this.style.color='black'" onmouseout="this.style.color='#6C757D'"></i>
                <i *ngIf="addrObj.deletable" class="fa fa-trash-alt font-16 ms-1 icon-button" style="color: #6C757D" (click)="deleteAddress(addrObj)" onmouseover="this.style.color='black'" onmouseout="this.style.color='#6C757D'"></i>
              </div>
            </div>
            <div *ngIf="addrObj.empty" class="p-2">
              <app-address-search
                [id]="addrObj.containerId!"
                [compactView]="true"
                [inputAddress]="addrObj.address"
                [description]="!workOrderView ? addrObj.workOrderIds.length < 2 ? addrObj.workOrder.work_order_title : ('orderDetails.addresses.multipleWorkOrders' | translate) : ''"
                [emitSelectedOnInputAddressChange]="false"
                [emitSelectedOnNameChange]="false"
                [removable]="false"
                [showCross]="false"
                [showUnitDetails]="false"
                [prefetchedAddresses]="affiliateAddresses"
                (onAddressSelect)="onAddressSelected($event, addrObj)"
                (onAddressRemove)="deleteAddress(addrObj)"
              ></app-address-search>
            </div>
          </div>
          <div [id]="'flush-collapse-' + j" [ngbCollapse]="addrObj.collapsed" class="accordion-collapse collapse" [attr.aria-labelledby]="'flush-heading-' + j" data-bs-parent="#accordionFlushExample">
            <div class="accordion-body">
              <div class="d-flex justify-content-between">
                <div class="col-md-6">
                  <div class="mb-1">
                    <i class="font-14 fa-regular fa-house"></i>
                    {{ "orders.newOrder.address.type" | translate }}: {{ addrObj.address.property_type_name }}
                  </div>
                  <div class="mb-1">
                    <i class="fa-regular fa-bed-front"></i>
                    {{ "orders.newOrder.address.bedrooms" | translate }}: {{ addrObj.address.number_of_bedrooms }}
                  </div>
                  <div class="mb-1">
                    <i class="fa-regular fa-door-open"></i>
                    {{ "orders.newOrder.address.garage" | translate }}: {{ verifyValue(addrObj.address.has_garage) }}
                  </div>
                  <div class="mb-1">
                    <i class="mdi mdi-garage-open-variant"></i>
                    {{ "orders.newOrder.address.totalRooms" | translate }}: {{ addrObj.address.number_of_rooms }}
                  </div>
                  <div *ngIf="addrObj.address.property_type_id === 2 || addrObj.address.property_type_id === 3">
                    <i class="fa-regular fa-building"></i>
                    {{ "orders.newOrder.address.sectionId" | translate }}: {{ addrObj.address.section_id }}
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-1">
                    <i class="fa-regular fa-draw-square"></i>
                    {{ "orders.newOrder.address.size" | translate }}:
                    <span *ngIf="addrObj.address.livable_area">{{ addrObj.address.livable_area }} m<sup>2</sup></span>
                    <span *ngIf="!addrObj.address.livable_area">{{ "unknown" | translate }}</span>
                  </div>
                  <div class="mb-1">
                    <i class="fa-regular fa-bath"></i>
                    {{ "orders.newOrder.address.bathrooms" | translate }}: {{ addrObj.address.number_of_bathrooms }}
                  </div>
                  <div class="mb-1">
                    <i class="fa-regular fa-elevator"></i>
                    {{ "orders.newOrder.address.elevator" | translate }}: {{ verifyValue(addrObj.address.has_elevator) }}
                  </div>
                  <div>
                    <i class="fa-regular fa-apartment"></i>
                    {{ "orders.newOrder.address.floors" | translate }}: {{ addrObj.address.number_of_floors }}
                  </div>
                </div>
              </div>

<!--                <div *ngIf="address.iframeVisible">-->
<!--                  <iframe [src]="address.addressUrl" height="450" width="100%" title="Google Street View"></iframe>-->
<!--                </div>-->
<!--                <div *ngIf="address.messageVisible" class="text-muted">-->
<!--                  Street View not available for this location.-->
<!--                </div>-->

              <div *ngIf="addresses[j]?.messageVisible" class="text-muted pt-1">{{ "orderDetails.jobAddress.streetview.notAvailable" | translate }}</div>
            </div>
          </div>
        </div>
      </div>
      <div *ngIf="totalDistanceKms > 0" class="p-2" style="border-top: 1px solid #DEE2E7;">
        <!-- Trip Type Toggle -->
        <div class="d-flex align-items-center mb-3">
          <app-toggle-switch
            [state]="isRoundTrip"
            [customId]="'tripTypeToggle'"
            [bigSwitch]="true"
            (stateChange)="onTripTypeToggle($event)"
          ></app-toggle-switch>
          <label class="ms-2 mb-0 fw-bold">
            {{ isRoundTrip ? ('orders.orderDetails.addressCard.roundTrip' | translate) : ('orders.orderDetails.addressCard.oneWay' | translate) }}
          </label>
        </div>

        <!-- Distance and Time Display -->
        <div>
          <span class="fw-bold font-16">{{'orders.orderDetails.addressCard.totalDistance' | translate}}: </span>
          <span class="font-16">{{ round(totalDistanceKms) }} km</span>
        </div>
        <div class="mt-2">
          <span class="fw-bold font-16">{{'orders.orderDetails.addressCard.totalTime' | translate}}: </span>
          <span class="font-16">{{ getFormattedDrivingTime() }}</span>
        </div>
      </div>
      <div *ngIf="showMap" class="border-preview overflow-hidden" style="height: 300px; overflow: hidden">
        <google-map
          *ngIf="companyPosition || addresses.length > 0"
          [center]="companyPosition"
          [zoom]="6"
          [options]="mapsOptions"
          height="300px"
          width="100%">
          <map-directions-renderer
            *ngIf="gMapsAddresses.length > 0 && isRoundTrip && (directionsResultsWithReturn$ | async) as directionsResults"
            [directions]="directionsResults"
            [options]="directionsRendererOptions">
          </map-directions-renderer>
          <map-directions-renderer
            *ngIf="gMapsAddresses.length > 0 && !isRoundTrip && (directionsResultsWithoutReturn$ | async) as directionsResults"
            [directions]="directionsResults"
            [options]="directionsRendererOptions">
          </map-directions-renderer>
        </google-map>
      </div>
    </div>
  </ng-container>
</app-card>
