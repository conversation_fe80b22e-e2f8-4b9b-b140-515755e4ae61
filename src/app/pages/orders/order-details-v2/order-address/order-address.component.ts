import {Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {DetailsViewSettings, OrderResponse, WorkOrderResponse} from "../../../../@shared/models/order.interfaces";
import {CompanyResponse} from "../../../../@shared/models/company.interfaces";
import {_CRM_ORD_118, _CRM_ORD_144, _CRM_ORD_177, CRM_COY_1, UnitDetails} from "../../../../@shared/models/input.interfaces";
import {DomSanitizer, SafeUrl} from "@angular/platform-browser";
import {AddressService} from "../../../../@shared/services/address.service";
import {CompanyService} from "../../../../@shared/services/company.service";
import {StorageService} from "../../../../@core/services/storage.service";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {StreetViewModalComponent} from "./_modals/street-view-modal/street-view-modal.component";
import {OrderService} from "../../../../@shared/services/order.service";
import {EditAddressModalComponent} from "../../../../@shared/components/address-search/edit-address-modal/edit-address-modal.component";
import {TranslateService} from "@ngx-translate/core";
import {VerifyPopupModal} from "../../../../@shared/components/verify-popup-modal/verify-popup-modal";
import {map, takeUntil} from "rxjs/operators";
import {forkJoin, Observable, Subject} from "rxjs";
import {GoogleMap, MapDirectionsRenderer, MapDirectionsService} from "@angular/google-maps";
import {convertCompactAddressToUnitDetails} from "../../../../@core/utils/utils.service";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {FormControl} from "@angular/forms";
import {ToastService} from "../../../../@core/services/toast.service";
import {round} from "@popperjs/core/lib/utils/math";
import {CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList, moveItemInArray} from "@angular/cdk/drag-drop";
import {StandardImports} from "../../../../@shared/global_import";
import {AddressSearchComponent} from "../../../../@shared/components/address-search/address-search.component";
import {AffiliateService} from "../../../../@shared/services/affiliate.service";
import {ToggleSwitchComponent} from "../../../../@shared/components/toggle-switch/toggle-switch.component";

export interface AddressObject {
  address: UnitDetails;
  containerId: number | null
  addressId: number | null;
  addressText: string | null;
  addressUrl: SafeUrl | null;
  streetValid: boolean;
  addressName: string | null;
  iframeVisible: boolean;
  messageVisible: boolean;
  collapsed: boolean;
  deletable: boolean;
  empty: boolean;
  workOrderIds: number[];
  workOrder: WorkOrderResponse;
  nameControl: FormControl;
  index: number | null;
  editMode: boolean;
  created_at: string | null;
}

@Component({
  selector: 'app-order-address',
  templateUrl: './order-address.component.html',
  styleUrls: ['./order-address.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent, CdkDropList, CdkDrag, CdkDragHandle, AddressSearchComponent, NgbCollapse, GoogleMap, MapDirectionsRenderer, ToggleSwitchComponent]
})

export class OrderAddressComponent implements OnInit, OnDestroy {
  @Input() workOrderView: boolean = false;
  @Input() viewSettings?: DetailsViewSettings;
  workOrder?: WorkOrderResponse;
  order?: OrderResponse;
  companyData: CompanyResponse;
  addresses: AddressObject[] = [];
  gMapsAddresses: UnitDetails[] = [];
  nonToggleHover: boolean = false;
  expandedAddressIds: number[] = [];
  directionsResultsWithoutReturn$: Observable<google.maps.DirectionsResult | undefined>;
  directionsResultsWithReturn$: Observable<google.maps.DirectionsResult | undefined>;
  companyAddress: UnitDetails;
  companyPosition: google.maps.LatLngLiteral;
  showMap: boolean = false;
  totalDistanceKms: number = 0;
  totalDrivingTimeMins: number= 0;

  // Round trip vs One way toggle
  isRoundTrip: boolean = true;
  roundTripDistanceKms: number = 0;
  roundTripDrivingTimeMins: number = 0;
  oneWayDistanceKms: number = 0;
  oneWayDrivingTimeMins: number = 0;

  affiliateAddresses: UnitDetails[] = [];

  mapsOptions: google.maps.MapOptions = {
    zoomControl: false,
    scrollwheel: true,
    disableDoubleClickZoom: true,
    mapTypeControl: false,
    streetViewControl: false,
    fullscreenControl: false,
    controlSize: 25,
  }
  directionsRendererOptions: google.maps.DirectionsRendererOptions = {}

  destroy$ = new Subject<void>();

  constructor(private sanitizer: DomSanitizer,
              private translate: TranslateService,
              private addressService: AddressService,
              private companyService: CompanyService,
              private storageService: StorageService,
              private modalService: NgbModal,
              private affiliateService: AffiliateService,
              private orderService: OrderService,
              private mapDirectionsService: MapDirectionsService,
              private toastService: ToastService) {}

  ngOnInit(): void {
    if (!this.workOrderView) {
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe((order) => {
        this.order = order;
        if (this.order.payment_recipient) {
          this.affiliateService.getAffiliateAddresses(this.order.payment_recipient.affiliate_id, true).subscribe((addresses: UnitDetails[]) => {
            this.affiliateAddresses = addresses;
          });
        }
        if (this.order) {
          this.prepareAddresses();
          this.getCompanyData();
        }
      });
    } else {
      this.orderService.workOrder$.pipe(takeUntil(this.destroy$)).subscribe((workOrder) => {
        this.workOrder = workOrder;
        if (this.workOrder?.order_id && !this.viewSettings?.contractorView) {
          this.orderService.getOrderById(this.workOrder.order_id).subscribe((order) => {
            if (order.payment_recipient) {
              this.affiliateService.getAffiliateAddresses(order.payment_recipient.affiliate_id, true).subscribe((addresses: UnitDetails[]) => {
                this.affiliateAddresses = addresses;
              });
            }
          });
        }
        this.prepareAddresses();
        this.getCompanyData();
      });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  prepareAddresses(): void {
    let containerId = 0;
    this.addresses = [];
    this.gMapsAddresses = [];
    let workOrders: WorkOrderResponse[] = this.workOrderView ? [this.workOrder!] : this.order?.work_orders!;
    for (const wo of workOrders) {
      for (const address of wo.addresses) {

        // Check if address already exists in the list
        let matchFound = false;
        for (const _address of this.addresses) {
          if (address.address_id === _address.addressId) {
            _address.workOrderIds.push(wo.work_order_id);
            matchFound = true;
            break;
          }
        }
        if (matchFound) {
          continue;
        }
        this.gMapsAddresses.push(address)
        const addressUrl = this.sanitizer.bypassSecurityTrustResourceUrl(`https://www.google.com/maps/embed/v1/streetview?key=AIzaSyBICuznrQvIXLH28fI-_Kkw_L4Zmst55sw&location=${address.lat},${address.lng}&heading=300&pitch=0&fov=35`);
        const addrObj: AddressObject = {
          address: address,
          addressId: address.address_id!,
          index: address.index,
          containerId: null,
          addressText: address.display!,
          addressUrl: addressUrl,
          streetValid: true,
          addressName: address.address_name,
          iframeVisible: true,
          messageVisible: false,
          collapsed: !this.expandedAddressIds.includes(address.address_id!),
          deletable: true,
          empty: false,
          workOrder: wo,
          workOrderIds: [wo.work_order_id],
          nameControl: new FormControl(address.address_name),
          editMode: false,
          created_at: address.created_at,
        };
        this.addresses.push(addrObj);
        this.checkStreetAddress(`${address.lat},${address.lng}`,this.addresses.length - 1);
        this.showMap = true;
      }

      for (const addressName of wo.additional_data?.addresses || []) {
        // Check if any empty addresses with same name exists
        let matchFound = false;
        for (const _address of this.addresses) {
          if (_address.empty && _address.addressName === addressName) {
            _address.workOrderIds.push(wo.work_order_id);
            matchFound = true;
            break;
          }
        }

        if (matchFound) {
          continue;
        }

        this.addresses.push({
          address: {address_name: addressName} as UnitDetails,
          addressId: null,
          index: null,
          containerId: containerId,
          addressText: null,
          addressUrl: null,
          streetValid: false,
          addressName: addressName,
          iframeVisible: false,
          messageVisible: false,
          collapsed: true,
          deletable: true,
          empty: true,
          workOrder: wo,
          workOrderIds: [wo.work_order_id],
          nameControl: new FormControl(addressName),
          editMode: false,
          created_at: null
        });
        containerId++;
      }

      if (this.addresses.length === 0) {
        this.addresses.push({
          address: {} as UnitDetails,
          addressId: null,
          index: null,
          containerId: containerId,
          addressText: null,
          addressUrl: null,
          streetValid: false,
          addressName: null,
          iframeVisible: false,
          messageVisible: false,
          collapsed: true,
          deletable: true,
          empty: true,
          workOrder: wo,
          workOrderIds: [wo.work_order_id],
          nameControl: new FormControl(''),
          editMode: false,
          created_at: null
        });
        containerId++;
      }

    }
    this.addresses.sort((a, b) => {
      const aIndex = a.index != null ? a.index : 999;
      const bIndex = b.index != null ? b.index : 999;
      return aIndex - bIndex;
    });

    this.gMapsAddresses.sort((a, b) => {
      const aIndex = a.index != null ? a.index : 999;
      const bIndex = b.index != null ? b.index : 999;
      return aIndex - bIndex;
    });

    if (this.companyAddress) {
      this.setDirections(this.companyAddress, this.gMapsAddresses);
    }
  }

  setDirections(origin: UnitDetails, addresses: UnitDetails[]) {
    if (addresses.length == 0) {
      return;
    }

    // Calculate round trip distance
    let requestWithReturn = this.createDirectionRequest(origin, addresses, true);
    this.directionsResultsWithReturn$ = this.mapDirectionsService.route(requestWithReturn).pipe(map(response => response.result));
    this.directionsResultsWithReturn$.subscribe((res) => {
      this.roundTripDistanceKms = 0;
      this.roundTripDrivingTimeMins = 0;

      if (res) {
        for (const route of res.routes) {
          for (const leg of route.legs) {
            if (leg.distance) {
              this.roundTripDistanceKms += leg.distance.value / 1000;
            }
            if (leg.duration) {
              this.roundTripDrivingTimeMins += leg.duration.value / 60;
            }
          }
        }
      }
      this.updateDisplayedValues();
    });

    // Calculate one-way distance
    let requestWithoutReturn = this.createDirectionRequest(origin, addresses, false);
    this.directionsResultsWithoutReturn$ = this.mapDirectionsService.route(requestWithoutReturn).pipe(map(response => response.result));
    this.directionsResultsWithoutReturn$.subscribe((res) => {
      this.oneWayDistanceKms = 0;
      this.oneWayDrivingTimeMins = 0;

      if (res) {
        for (const route of res.routes) {
          for (const leg of route.legs) {
            if (leg.distance) {
              this.oneWayDistanceKms += leg.distance.value / 1000;
            }
            if (leg.duration) {
              this.oneWayDrivingTimeMins += leg.duration.value / 60;
            }
          }
        }
      }
      this.updateDisplayedValues();
    });
  }

  updateDisplayedValues() {
    if (this.isRoundTrip) {
      this.totalDistanceKms = this.roundTripDistanceKms;
      this.totalDrivingTimeMins = this.roundTripDrivingTimeMins;
    } else {
      this.totalDistanceKms = this.oneWayDistanceKms;
      this.totalDrivingTimeMins = this.oneWayDrivingTimeMins;
    }
  }

  onTripTypeToggle(isRoundTrip: boolean) {
    this.isRoundTrip = isRoundTrip;
    this.updateDisplayedValues();
  }

  getFormattedDrivingTime(): string {
    const totalMinutes = this.totalDrivingTimeMins;
    if (!totalMinutes) return '';

    const days = Math.floor(totalMinutes / (24 * 60));
    const hours = Math.floor((totalMinutes - days * 24 * 60) / 60);
    const minutes = Math.floor(totalMinutes % 60);

    let result = '';
    if (days > 0) {
      result += `${days} ${this.translate.instant(days > 1 ? 'DAYS' : 'DAY')} `;
    }
    if (hours > 0 || days > 0) {
      result += `${hours} ${this.translate.instant(hours > 1 ? 'HOURS' : 'HOUR')} `;
    }
    result += `${minutes} ${this.translate.instant(minutes > 1 ? 'MINUTES' : 'MINUTE')}`;
    return result;
  }


  createDirectionRequest(origin: UnitDetails, addresses: UnitDetails[], includeReturn: boolean) {
    let waypoints: google.maps.DirectionsWaypoint[] = [];
    for (const address of addresses) {
      if (!address.lat || !address.lng) {
        continue;
      }
      waypoints.push({ location: new google.maps.LatLng(address.lat!, address.lng)});
    }
    let request: google.maps.DirectionsRequest;

    if (includeReturn) {
      request = {
        origin: {lat: origin.lat!, lng: origin.lng!}, // Example origin coordinates
        waypoints: waypoints,
        destination: {lat: origin.lat!, lng: origin.lng!}, // Example destination coordinates
        travelMode: google.maps.TravelMode.DRIVING // Specify the travel mode
      };
    }
    else {
      let destination = waypoints.pop();
      request = {
        origin: {lat: origin.lat!, lng: origin.lng!}, // Example origin coordinates
        waypoints: waypoints,
        destination: destination?.location as google.maps.LatLng,
        travelMode: google.maps.TravelMode.DRIVING // Specify the travel mode
      };
      }
    return request;
  }

  checkStreetAddress(address: string, idx: number) {
    this.addressService.validateAddressStreeView(`https://maps.googleapis.com/maps/api/streetview/metadata?key=AIzaSyBICuznrQvIXLH28fI-_Kkw_L4Zmst55sw&location=${address}`)
      .subscribe({
        next: (response: any) => {
          if (response.status === 'ZERO_RESULTS') {
            this.addresses[idx].streetValid = false;
            this.addresses[idx].iframeVisible = false;
            this.addresses[idx].messageVisible = true;
          }
        },
        error: (error: any) => {
          console.error('Error fetching google maps data:', error);
        }
      });
  }

  getCompanyData(){
    if (this.companyData) {
      return;
    }
    let params: CRM_COY_1 = {
      company_id: this.storageService.getSelectedCompanyId(),
    };
    this.companyService.getCompanyDataByCompanyId(params).subscribe((company) => {
      this.companyData = company;
      this.companyAddress = convertCompactAddressToUnitDetails(company.address)!;
      this.companyPosition = {lat: this.companyAddress.lat!, lng: this.companyAddress.lng!}
      if (this.gMapsAddresses.length > 0) {
        this.setDirections(this.companyAddress, this.gMapsAddresses);
      }
    });
  }

  addEmptyAddress() {
    if (this.addresses.find((addr) => addr.containerId === -1)) return;
    this.addresses.push({
      address: {} as UnitDetails,
      addressId: null,
      index: null,
      containerId: -1,
      addressText: null,
      addressUrl: null,
      streetValid: false,
      addressName: null,
      iframeVisible: false,
      messageVisible: false,
      collapsed: true,
      deletable: true,
      empty: true,
      workOrder: this.workOrder || {work_order_id: -1} as WorkOrderResponse,
      workOrderIds: [],
      nameControl: new FormControl(''),
      editMode: false,
      created_at: null
    });
  }

  async updateAddressName(addrObj: AddressObject) {
    if (this.viewSettings?.contractorNotAcceptedView) return;
    if (!addrObj.nameControl.value) {
      return;
    }
    if (addrObj.addressName === addrObj.nameControl.value) {
      return;
    }
    addrObj.editMode = false;
    this.addressService.updateAddress({...addrObj.address, address_name: addrObj.nameControl.value}).subscribe(async () => {
      await this.updateChildren();
      this.toastService.successToast('updated')
      if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
        this.orderService.fetchAndUpdateOrder((this.order?.order_id || this.workOrder?.order_id!), 'addressNameUpdated');
      }
    });
  }

  async updateChildren() {
    if (this.workOrder?.schedule?.num_unstarted_work_orders! > 1) {
      let modalRef = this.modalService.open(VerifyPopupModal, {backdrop: 'static'});
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.titleTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.title';
      modalRef.componentInstance.bodyBoldTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyBoldTranslationKey';
      modalRef.componentInstance.bodyMutedTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyMutedTranslationKey';
      modalRef.componentInstance.bodyRegularTranslationKey = 'workOrderDetails.repeatingView.updateChildrenModal.bodyRegularTranslationKey.addressUpdate';
      try {
        let updateChildren = await modalRef.result;
        if (updateChildren) {
          let payload: _CRM_ORD_118 = {
              work_order_id: this.workOrder!.work_order_id,
              update_children_addresses: true,
          }
          this.orderService.patchWorkOrder(payload).subscribe(() => {
            this.orderService.workOrderListUpdated$.next();
          });
        }
      } catch (e) {
      }
    }
  }

  resetAddressName(addrObj: AddressObject) {
    if (this.viewSettings?.contractorNotAcceptedView) return;
    addrObj.nameControl.setValue(addrObj.addressName);
    addrObj.editMode = false;
  }

  async openEditAddressModal(addrObj: AddressObject) {
    if (this.viewSettings?.contractorNotAcceptedView) return;
    const modalRef = this.modalService.open(EditAddressModalComponent, {size: "md"})
    modalRef.componentInstance.address = JSON.parse(JSON.stringify(addrObj.address));
    modalRef.componentInstance.showChangeAddressButton = true;
    modalRef.result.then((result: UnitDetails) => {
      if (result) {
        if (!result.address_id) {
          result.address_id = addrObj.address.address_id;
        }
        this.addressService.updateAddress(result).subscribe(async () => {
          this.addresses.find((addr) => addr.addressId === addrObj.addressId)!.address = result;
          if (this.workOrderView) {
            this.workOrder!.addresses = this.addresses.map((addr) => addr.address);
          }
          await this.updateChildren();
          if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
            this.orderService.fetchAndUpdateOrder((this.order?.order_id || this.workOrder?.order_id!), 'openEditAddressModal.then');
          }
          this.prepareAddresses();
        });
      } else {
        return;
    }
  }).catch((error) => {
    return;
  });
  }

  toggleCollapse(addrObj: AddressObject) {
    if (addrObj.empty) return;
    if (!this.nonToggleHover) {
      addrObj.collapsed = !addrObj.collapsed;
      if (!addrObj.collapsed) {
        this.expandedAddressIds.push(addrObj.addressId!);
      } else {
        this.expandedAddressIds = this.expandedAddressIds.filter(id => id !== addrObj.addressId);
      }
    }
  }

  verifyValue(value: number | null): string {
    if (value === 1) {
      return this.translate.instant('yes');
    }
    else if (value === 0) {
      return this.translate.instant('no');
    }
    else {
    return this.translate.instant('unknown');
    }
  }

  async onAddressSelected(addressContainer: any, addrObj: AddressObject) {
    if (this.viewSettings?.contractorNotAcceptedView) return;
    // Add address for all work orders
    if ((addrObj.workOrder.work_order_id === -1 && this.order?.work_orders && this.order?.work_orders.length > 1) || addrObj.workOrderIds.length > 1) {
      const modalRef = this.modalService.open(VerifyPopupModal);
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.bodyBoldTranslationKey = 'orderDetails.addresses.multiModal.boldKey'
      modalRef.componentInstance.bodyRegularTranslationKey = 'orderDetails.addresses.multiModal.regularKey'
      modalRef.result.then((result) => {
        if (result) {
          let payload: _CRM_ORD_144 = {
            work_order_ids: this.order!.work_orders.map((wo) => wo.work_order_id),
            address: addressContainer.address
          }
          this.orderService.addAddressToMultipleWorkOrders(payload).subscribe(() => {
            if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
              this.orderService.fetchAndUpdateOrder((this.order?.order_id!), 'onAddressSelected.then.multiple');
            }
            this.prepareAddresses();
          });
        } else {
          this.addresses = this.addresses.filter((address) => address.containerId !== -1);
          return;
        }
      });
    }
    // Add address for single work order
    else {
      if (addrObj.workOrder.work_order_id === -1) {
        addrObj.workOrder = this.order!.work_orders[0];
      }

      let workOrderAddressNamesToKeep: string[] = [];
      if (addrObj.workOrder.additional_data?.addresses) {
        workOrderAddressNamesToKeep = addrObj.workOrder.additional_data?.addresses!.filter((address) => address !== addrObj.addressName);
      }

      const payload: _CRM_ORD_118 = {
        work_order_id: addrObj.workOrder.work_order_id,
        addresses: [addressContainer.address],
        additional_data: {
          ...addrObj.workOrder.additional_data,
          addresses: workOrderAddressNamesToKeep
        }
      }

      this.orderService.patchWorkOrder(payload).subscribe(async (res) => {
        if (this.workOrderView) {
          this.workOrder = res;
          await this.updateChildren();
          this.prepareAddresses();
        }
        if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
          this.orderService.fetchAndUpdateOrder((this.order?.order_id || this.workOrder?.order_id!), 'onAddressSelected.then');
        }
        this.prepareAddresses();
      });
    }
  }

  openStreetViewModal(addrObj: AddressObject) {
    const selectedAddress = {
      address: addrObj.address,
      addressText: addrObj.address.display,
      addressUrl: this.sanitizer.bypassSecurityTrustResourceUrl(`https://www.google.com/maps/embed/v1/streetview?key=AIzaSyBICuznrQvIXLH28fI-_Kkw_L4Zmst55sw&location=${addrObj.address.lat},${addrObj.address.lng}&heading=300&pitch=0&fov=35`),
      streetValid: true,
      addressName: addrObj.addressName
    };

    const modalRef = this.modalService.open(StreetViewModalComponent, { size: 'lg' });
    modalRef.componentInstance.address = selectedAddress;
  }

  deleteAddress(addrObj: AddressObject) {
    if (this.viewSettings?.contractorNotAcceptedView) return;
    const modalRef = this.modalService.open(VerifyPopupModal);
    if (addrObj.workOrderIds.length > 1) {
      modalRef.componentInstance.showBody = true;
      modalRef.componentInstance.bodyBoldTranslationKey = 'orderDetails.addresses.deleteModal.multiple.boldKey'
      modalRef.componentInstance.bodyRegularTranslationKey = 'orderDetails.addresses.deleteModal.multiple.regularKey'
    }
    modalRef.result.then((result) => {
      if (result) {
        if (addrObj.workOrderIds.length > 1) {
          const requests = addrObj.workOrderIds.map((workOrderId) => {
            return this.orderService.patchWorkOrder({
              work_order_id: workOrderId,
              deleted_address_ids: [addrObj.addressId!]
            });
          });
          forkJoin(requests).subscribe(() => {
            if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
              this.orderService.fetchAndUpdateOrder((this.order?.order_id || this.workOrder?.order_id!), 'deleteAddress.then.multiple');
            }
          });
        } else {
          const payload: _CRM_ORD_118 = {
            work_order_id: addrObj.workOrder.work_order_id,
            deleted_address_ids: [addrObj.addressId!],
          }

          this.orderService.patchWorkOrder(payload).subscribe(async (res) => {
            await this.updateChildren();
            if (this.workOrderView) {
              this.workOrder = res;
              this.prepareAddresses();
            }
            if (!this.workOrder || this.storageService.ownedByCompany(this.workOrder.company_id)) {
              this.orderService.fetchAndUpdateOrder((this.order?.order_id || this.workOrder?.order_id!), 'onAddressSelected.then');
            }
          });
        }
      }
    });
  }

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.addresses, event.previousIndex, event.currentIndex);
    for (let i = 0; i < this.addresses.length; i++) {
      this.addresses[i].index = i;
    }

    if (this.addresses[event.currentIndex].addressId === null) return;

    let payload: _CRM_ORD_177 = {
      work_order_id: this.addresses[event.currentIndex].workOrder.work_order_id,
      address_id: this.addresses[event.currentIndex].addressId!,
      index: event.currentIndex
    }
      this.orderService.updateWorkOrderAddress(payload).subscribe((res) => {
        if (!this.viewSettings?.workOrderView) {
          this.orderService.fetchAndUpdateWorkOrder(this.addresses[event.currentIndex].workOrder.work_order_id, !!this.order, 'addressDrop')
        } else {
          this.orderService.fetchAndUpdateOrder(this.addresses[event.currentIndex].workOrder.order_id, 'addressDrop')
        }
        for (let i = 0; i < this.addresses.length; i++) {
          this.addresses[i].index = i;
        }
        this.addresses.sort((a, b) => {
          const aIndex = a.index != null ? a.index : 999;
          const bIndex = b.index != null ? b.index : 999;
          return aIndex - bIndex;
        });

        this.gMapsAddresses.sort((a, b) => {
          const aIndex = a.index != null ? a.index : 999;
          const bIndex = b.index != null ? b.index : 999;
          return aIndex - bIndex;
        });

        if (this.companyAddress) {
          this.setDirections(this.companyAddress, this.gMapsAddresses);
        }

    });
  }

  protected readonly round = round;
}
